# Mini Game Collection

A collection of classic games built with React, TypeScript, and Vite. This project features a modern, responsive design with a beautiful UI using Tailwind CSS.

## 🎮 Games

- **Sudoku**: A classic number puzzle game with multiple difficulty levels
  - Real-time error highlighting
  - Multiple difficulty levels (Easy, Medium, Hard)
  - Clean, modern interface
  - Responsive design
- **Minesweeper**: Test your deduction skills in this classic game
  - Three difficulty levels (Beginner, Intermediate, Expert)
  - Flag placement
  - Timer and mine counter
- **Sliding Puzzle**: Arrange the numbered tiles in order
  - Customizable grid size
  - Move counter and timer

## 🚀 Features

- Built with React 18 and TypeScript
- Modern UI with Tailwind CSS
- Responsive design for all devices
- Dark/Light mode support

## 🛠️ Tech Stack

- React 18
- TypeScript
- Vite
- Tailwind CSS
- Radix UI Components
- React Router DOM
- Lucide Icons

## 🏗️ Development

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

## 📦 Project Structure

```
src/
├── components/     # Reusable UI components
├── pages/         # Page components
├── data/          # Game data and utilities
├── assets/        # Static assets
├── lib/           # Utility functions
└── styles/        # Global styles
```

## 🎨 UI Components

The project uses a combination of custom components and Radix UI primitives:

- Button
- Alert Dialog
- Hover Card
- Switch
- Theme Toggle

## 🌐 Deployment

The project is deployed at [minigame.dydxsoft.my](https://minigame.dydxsoft.my) using GitHub Pages.

## 📝 License

MIT License - feel free to use this project for your own purposes.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request. 
